# Open WebUI Docker Compose Setup Guide

This project provides a comprehensive Docker Compose setup for Open WebUI with LiteLLM as an AI gateway and various supporting services. This guide will walk you through the complete setup process.

## Prerequisites

- Docker and Docker Compose installed on your system
- Git (for cloning the repository)
- Basic understanding of Docker and environment variables

## Project Structure

The project is organized with modular Docker Compose files to allow flexible service combinations:

- `docker-compose.yml` - Core services (Open WebUI and LiteLLM)
- `docker-compose.qdrant.yml` - Qdrant vector database for RAG functionality
- `docker-compose.redis.yml` - Redis for session management and caching
- `docker-compose.postgres.yml` - PostgreSQL database (alternative/additional storage)
- `docker-compose.s3.yml` - MinIO S3-compatible object storage
- `docker-compose.documents.yml` - Document processing services

## Step 1: Environment Configuration

### Copy Environment Template

Navigate to the `_docker` directory and copy the example environment file:

```bash
cd _docker
cp .env.example .env
```

### Configure Required Environment Variables

Edit the `.env` file and set the following essential variables:

#### Core Settings

```bash
# Open WebUI Configuration
WEBUI_SECRET_KEY=your-secure-webui-secret-key-here
OPENWEBUI_PORT=3000

# LiteLLM Configuration
LITELLM_MASTER_KEY=sk-your-litellm-master-key-here
LITELLM_SALT_KEY=your-litellm-salt-key-here
LITELLM_PORT=4000
```

#### AI Provider API Keys

Configure at least one AI provider in the `.env` file:

**Azure OpenAI:**

```bash
AZURE_API_KEY=your-azure-openai-api-key-here
AZURE_API_BASE=https://your-resource-name.openai.azure.com
AZURE_EMBEDDING_API_KEY=your-azure-embedding-api-key-here
AZURE_EMBEDDING_API_BASE=https://your-embedding-resource-name.cognitiveservices.azure.com
```

**Google Gemini:**

```bash
GOOGLE_GENAI_API_KEY=your-google-genai-api-key-here
```

**Hugging Face:**

```bash
HF_TEI_API_KEY=your-huggingface-tei-api-key-here
HF_TEI_API_BASE=http://your-hf-tei-server:8081
```

## Step 2: LiteLLM AI Gateway Configuration

LiteLLM acts as the AI gateway that manages all AI model requests. You can configure it in two ways:

### Option 1: Configuration File (Recommended)

The project includes a pre-configured `litellm-config.yaml` file in `_docker/config/`. This file references the environment variables you set in Step 1 and includes:

- Azure OpenAI models (gpt-4.1-mini, text-embedding-3-small)
- Hugging Face models (Qwen3-Embedding-0.6B, BAAI/bge-reranker-large)
- Google Gemini embedding models

### Option 2: LiteLLM UI Configuration

After starting the services, you can also configure models through the LiteLLM web interface at `http://localhost:4000`.

### Virtual Key Generation

Once LiteLLM is running, you must generate a virtual key to connect Open WebUI to LiteLLM:

1. Access the LiteLLM UI at `http://localhost:4000`
2. Navigate to the Virtual Keys section
3. Generate a new virtual key
4. Use this virtual key to configure Open WebUI

For detailed instructions, refer to the [LiteLLM Virtual Keys Documentation](https://docs.litellm.ai/docs/proxy/virtual_keys).

## Step 3: Service Deployment

The Docker Compose setup is designed to be modular. You can start with the core services and add additional services as needed.

### Core Services Only

Start with the essential services (Open WebUI and LiteLLM):

```bash
docker compose --env-file .env -f docker-compose.yml up -d
```

### Adding Vector Database (Qdrant)

For RAG (Retrieval-Augmented Generation) functionality, add Qdrant:

```bash
docker compose --env-file .env.qdrant --env-file .env -f docker-compose.yml -f docker-compose.qdrant.yml up -d
```

### Adding Session Management (Redis)

For improved session handling and caching:

```bash
docker compose --env-file .env.redis --env-file .env -f docker-compose.yml -f docker-compose.redis.yml up -d
```

### Adding Database Support (PostgreSQL)

For additional database functionality:

```bash
docker compose --env-file .env.postgresql --env-file .env -f docker-compose.yml -f docker-compose.postgres.yml up -d
```

### Adding Object Storage (MinIO S3)

For file storage and document management:

```bash
docker compose --env-file .env.s3 --env-file .env -f docker-compose.yml -f docker-compose.s3.yml up -d
```

### Complete Stack Deployment

To deploy all services at once:

```bash
docker compose --env-file .env.qdrant --env-file .env --env-file .env.redis --env-file .env.postgresql --env-file .env.s3 -f docker-compose.yml -f docker-compose.qdrant.yml -f docker-compose.redis.yml -f docker-compose.postgres.yml -f docker-compose.s3.yml up -d
```

## Step 4: Service Configuration

### Qdrant Configuration

If using Qdrant, configure the following in `.env.qdrant`:

```bash
QDRANT_API_KEY=your_secure_qdrant_api_key_here
```

### Redis Configuration

If using Redis, configure the following in `.env.redis`:

```bash
REDIS_PASSWORD=your_secure_redis_password_here
```

### PostgreSQL Configuration

If using PostgreSQL, configure the following in `.env.postgresql`:

```bash
POSTGRES_PASSWORD=your_secure_postgres_password_here
```

### MinIO S3 Configuration

If using MinIO, configure the following in `.env.s3`:

```bash
MINIO_ROOT_PASSWORD=your_secure_minio_password_here
```

## Step 5: Access and Initial Setup

### Service Access Points

- **Open WebUI**: `http://localhost:3000`
- **LiteLLM Gateway**: `http://localhost:4000`
- **Qdrant Dashboard**: `http://localhost:6333/dashboard` (if enabled)
- **MinIO Console**: `http://localhost:9001` (if enabled)

### Initial Configuration

1. Access Open WebUI at `http://localhost:3000`
2. Create your admin account (if `ENABLE_SIGNUP=true`)
3. Access LiteLLM at `http://localhost:4000`
4. Generate a virtual key in LiteLLM
5. Configure Open WebUI to use the LiteLLM gateway with your virtual key

## Health Checks and Monitoring

All services include health checks to ensure proper startup and operation. You can monitor service status using:

```bash
docker compose ps
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Ensure the configured ports (3000, 4000, 6333, 6379, 9000, 9001) are not in use by other services
2. **Environment Variables**: Verify all required environment variables are set in the respective `.env` files
3. **API Keys**: Ensure all AI provider API keys are valid and have sufficient permissions
4. **Service Dependencies**: Services have health check dependencies; allow time for all services to start properly

### Log Access

To view logs for specific services:

```bash
# Open WebUI logs
docker compose logs openwebui

# LiteLLM logs
docker compose logs litellm

# All services
docker compose logs
```

## Maintenance

### Updating Services

To update to the latest images:

```bash
docker compose pull
docker compose up -d
```

### Backup Data

Important data volumes to backup:

- `open-webui-data`: Open WebUI application data
- `litellm-postgres-data`: LiteLLM configuration and usage data
- `qdrant-data`: Vector database data (if using Qdrant)
- `redis-data`: Session data (if using Redis)
- `minio-data`: Object storage data (if using MinIO)

## Security Considerations

- Change all default passwords and API keys
- Use strong, unique values for `WEBUI_SECRET_KEY`, `LITELLM_MASTER_KEY`, and `LITELLM_SALT_KEY`
- Regularly rotate API keys and passwords
- Consider using Docker secrets for sensitive data in production environments
- Restrict network access to services that don't need external access

## Support and Documentation

- [Open WebUI Documentation](https://docs.openwebui.com/)
- [LiteLLM Documentation](https://docs.litellm.ai/)
- [LiteLLM Virtual Keys](https://docs.litellm.ai/docs/proxy/virtual_keys)
