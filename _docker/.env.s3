# MinIO S3-Compatible Storage Configuration
# Use this for file storage and document management

# =================================
# MinIO Access Configuration
# =================================
MINIO_ROOT_USER=openwebui
MINIO_ROOT_PASSWORD=your_secure_minio_password_here
MINIO_BUCKET_NAME=openwebui-files
MINIO_REGION=us-east-1

# =================================
# MinIO Connection Settings
# =================================
MINIO_HOST=minio
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_USE_SSL=false

# S3 Configuration for Open WebUI
S3_ENDPOINT_URL=http://${MINIO_HOST}:${MINIO_PORT}
S3_ACCESS_KEY_ID=${MINIO_ROOT_USER}
S3_SECRET_ACCESS_KEY=${MINIO_ROOT_PASSWORD}
S3_BUCKET_NAME=${MINIO_BUCKET_NAME}
S3_REGION=${MINIO_REGION}

# =================================
# File Storage Configuration
# =================================
ENABLE_S3_STORAGE=true
FILE_STORAGE_PROVIDER=s3
UPLOAD_DIR=/app/backend/data/uploads

# Document Upload Settings
MAX_FILE_SIZE=100MB
ALLOWED_FILE_EXTENSIONS=pdf,doc,docx,txt,md,csv,xlsx,pptx,jpg,jpeg,png,gif

# =================================
# MinIO Performance Settings
# =================================
MINIO_BROWSER=on
MINIO_COMPRESS=true
MINIO_COMPRESS_EXTENSIONS=.pdf,.doc,.docx,.txt,.md,.csv,.xlsx,.pptx
MINIO_COMPRESS_MIME_TYPES=application/*,text/*

# =================================
# MinIO Security Settings
# =================================
MINIO_BROWSER_REDIRECT_URL=http://localhost:9001
MINIO_SERVER_ACCESS_KEY=${MINIO_ROOT_USER}
MINIO_SERVER_SECRET_KEY=${MINIO_ROOT_PASSWORD}

# =================================
# Backup Configuration
# =================================
S3_BACKUP_ENABLED=true
S3_BACKUP_BUCKET=openwebui-backups
S3_BACKUP_RETENTION_DAYS=30

# =================================
# Development Settings
# =================================
MINIO_LOG_LEVEL=INFO
MINIO_UPDATE=off
