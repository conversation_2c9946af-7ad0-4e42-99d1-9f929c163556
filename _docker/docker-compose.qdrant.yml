# Qdrant Vector Database for RAG
# Usage: docker-compose -f docker-compose.yml -f docker-compose.qdrant.yml up -d

volumes:
  qdrant-data:
    driver: local

services:
  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: openwebui-qdrant
    restart: unless-stopped
    networks:
      - openwebui-network
    volumes:
      - qdrant-data:/qdrant/storage
      # - ./config/qdrant-config.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "${QDRANT_HTTP_PORT:-6333}:6333"
    environment:
      #   - QDRANT__LOG_LEVEL=${QDRANT_LOG_LEVEL:-INFO}
      #   - QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS=${QDRANT_MAX_SEARCH_THREADS:-0}
      #   - QDRANT__STORAGE__PERFORMANCE__MAX_OPTIMIZATION_THREADS=${QDRANT_MAX_OPTIMIZATION_THREADS:-1}
      - QDRANT__SERVICE__API_KEY=${QDRANT_API_KEY}
    env_file:
      - .env
      - .env.qdrant
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Override Open WebUI to use Qdrant
  openwebui:
    depends_on:
      qdrant:
        condition: service_healthy
      litellm:
        condition: service_healthy
    environment:
      # Qdrant Configuration
      - VECTOR_DB=qdrant
      - QDRANT_URI=http://qdrant:6333
      - QDRANT_API_KEY=${QDRANT_API_KEY:-}
      - QDRANT_COLLECTION_PREFIX=${QDRANT_COLLECTION_PREFIX:-openwebui}
    env_file:
      - .env
      - .env.qdrant
