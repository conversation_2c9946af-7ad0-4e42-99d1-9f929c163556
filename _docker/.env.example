# Core Open WebUI and LiteLLM Configuration
# Copy this to .env and customize for your setup

# =================================
# Open WebUI Core Settings
# =================================
# Basic configuration
ENV=dev
WEBUI_NAME=Open WebUI
WEBUI_URL=http://localhost:3000
OPENWEBUI_PORT=3000 # Only relevant if you're running it locally

# Authentication
ENABLE_SIGNUP=true
DEFAULT_USER_ROLE=pending
WEBUI_SECRET_KEY=your-secure-webui-secret-key-here

# Administration options
ENABLE_ADMIN_EXPORT=true
ENABLE_ADMIN_CHAT_ACCESS=false

# Database (SQLite by default for OpenWebUI, PostgreSQL for LiteLLM)
# DATABASE_URL=sqlite:///app/backend/data/webui.db  # This is for OpenWebUI only
# LiteLLM will use its own PostgreSQL DATABASE_URL from docker-compose

# =================================
# LiteLLM Gateway Settings
# =================================
LITELLM_MASTER_KEY=sk-your-litellm-master-key-here
LITELLM_SALT_KEY=your-litellm-salt-key-here
LITELLM_PORT=4000

# LiteLLM Database Configuration (PostgreSQL)
LITELLM_DATABASE_URL=************************************************************/litellm

# =================================
# Azure OpenAI Configuration
# =================================
# These environment variables are used by LiteLLM config (litellm-config.yaml)
# In the LiteLLM config, they are referenced as "os.environ/AZURE_API_KEY"
AZURE_API_KEY=your-azure-openai-api-key-here
AZURE_API_BASE=https://your-resource-name.openai.azure.com

# =================================
# Azure Embeddings Configuration
# =================================
# These environment variables are used by LiteLLM config for embedding models
# In the LiteLLM config, they are referenced as "os.environ/AZURE_EMBEDDING_API_KEY"
AZURE_EMBEDDING_API_BASE=https://your-embedding-resource-name.cognitiveservices.azure.com
AZURE_EMBEDDING_API_KEY=your-azure-embedding-api-key-here

# =================================
# Google GenAI Configuration
# =================================
# These environment variables are used by LiteLLM config for Gemini models
# In the LiteLLM config, they are referenced as "os.environ/GOOGLE_GENAI_API_KEY"
GOOGLE_GENAI_API_KEY=your-google-genai-api-key-here
GOOGLE_GENAI_API_BASE=https://generativelanguage.googleapis.com

# =================================
# Huggingface TEI Configuration
# =================================
# These environment variables are used by LiteLLM config for Hugging Face models
# In the LiteLLM config, they are referenced as "os.environ/HF_TEI_API_KEY"
HF_TEI_API_KEY=your-huggingface-tei-api-key-here
HF_TEI_API_BASE=http://your-hf-tei-server:8081

# =================================
# RAG (Retrieval-Augmented Generation) Configuration
# =================================
RAG_EMBEDDING_ENGINE=openai
RAG_EMBEDDING_MODEL=huggingface/Qwen/Qwen3-Embedding-0.6B
ENABLE_RAG_HYBRID_SEARCH=true

RAG_TOP_K=5
RAG_TOP_K_RERANKER=5

RAG_RELEVANCE_THRESHOLD=20.0
RAG_TEXT_SPLITTER=character

CHUNK_SIZE=1500
CHUNK_OVERLAP=300

RAG_OPENAI_API_BASE_URL=http://litellm:4000/
RAG_OPENAI_API_KEY=your-openai-api-key-for-rag

RAG_EMBEDDING_OPENAI_BATCH_SIZE=10
