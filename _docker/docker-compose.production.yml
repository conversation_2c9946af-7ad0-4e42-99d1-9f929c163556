# Production-ready configuration for production environment
# Usage: docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d

services:
  # Production overrides for Open WebUI
  openwebui:
    environment:
      # Production environment settings
      - ENV=prod
      - OFFLINE_MODE=false
      - ENABLE_VERSION_UPDATE_CHECK=false

      # Security hardening
      - WEBUI_AUTH=true
      - ENABLE_SIGNUP=false
      - DEFAULT_USER_ROLE=pending
      - WEBUI_SESSION_COOKIE_SECURE=true
      - WEBUI_AUTH_COOKIE_SECURE=true
      - WEBUI_SESSION_COOKIE_SAME_SITE=strict
      - WEBUI_AUTH_COOKIE_SAME_SITE=strict
      - ENABLE_WEB_LOADER_SSL_VERIFICATION=true

      # JWT Security
      - JWT_EXPIRES_IN=3600s

      # Admin restrictions
      - ENABLE_ADMIN_EXPORT=false
      - ENABLE_ADMIN_CHAT_ACCESS=false
      - SHOW_ADMIN_DETAILS=false

      # Performance optimizations
      - MODELS_CACHE_TTL=300
      - THREAD_POOL_SIZE=8
      - ENABLE_REALTIME_CHAT_SAVE=true
      - AIOHTTP_CLIENT_TIMEOUT=300
      - AIOHTTP_CLIENT_TIMEOUT_MODEL_LIST=30

      # Disable unnecessary features
      - ENABLE_CODE_EXECUTION=false
      - ENABLE_OLLAMA_API=false
      - USE_CUDA_DOCKER=false

      # RAG optimizations
      - RAG_EMBEDDING_BATCH_SIZE=10
      - ENABLE_RETRIEVAL_QUERY_GENERATION=true
      - RAG_FULL_CONTEXT=false

      # Logging
      - LOG_LEVEL=INFO
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: "1.0"
        reservations:
          memory: 1G
          cpus: "0.5"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Production overrides for LiteLLM
  litellm:
    environment:
      - LITELLM_LOG_LEVEL=INFO
      - LITELLM_REQUEST_TIMEOUT=600
      - LITELLM_NUM_RETRIES=3
      - LITELLM_FALLBACKS=true
      # Disable UI in production
      - UI_USERNAME=
      - UI_PASSWORD=
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "0.5"
        reservations:
          memory: 512M
          cpus: "0.25"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
