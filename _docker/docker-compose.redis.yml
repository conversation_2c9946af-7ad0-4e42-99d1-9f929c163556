# Redis for Session Management and Caching
# Usage: docker-compose -f docker-compose.yml -f docker-compose.redis.yml up -d

volumes:
  redis-data:
    driver: local

services:
  # Redis for session management and caching
  redis:
    image: redis:8.2-alpine
    container_name: openwebui-redis
    restart: unless-stopped
    networks:
      - openwebui-network
    volumes:
      - redis-data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD}"]
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    env_file:
      - .env
      - .env.redis
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # Override Open WebUI to use Redis
  openwebui:
    depends_on:
      redis:
        condition: service_healthy
      litellm:
        condition: service_healthy
    environment:
      # Redis Configuration
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - WEBSOCKET_REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/1
      - REDIS_KEY_PREFIX=${REDIS_KEY_PREFIX:-openwebui}
      - ENABLE_WEBSOCKET_SUPPORT=true
      - WEBSOCKET_MANAGER=redis
