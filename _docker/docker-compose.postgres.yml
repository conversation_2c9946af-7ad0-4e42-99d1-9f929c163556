# PostgreSQL Database for Open WebUI
# Usage: docker-compose -f docker-compose.yml -f docker-compose.postgres.yml up -d

volumes:
  postgres-data:
    driver: local

services:
  # PostgreSQL Database
  postgres:
    image: postgres:17-alpine
    container_name: openwebui-postgres
    restart: unless-stopped
    networks:
      - openwebui-network
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./config/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./config/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-openwebui}
      - POSTGRES_USER=${POSTGRES_USER:-openwebui}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=md5 --auth-local=trust
    env_file:
      - .env.postgresql
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -d ${POSTGRES_DB:-openwebui} -U ${POSTGRES_USER:-openwebui}",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf"]

  # Override Open WebUI to use PostgreSQL
  openwebui:
    depends_on:
      postgres:
        condition: service_healthy
      litellm:
        condition: service_healthy
    environment:
      # PostgreSQL Database Configuration
      - DATABASE_URL=postgresql://${POSTGRES_USER:-openwebui}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-openwebui}
      - DATABASE_POOL_SIZE=${DATABASE_POOL_SIZE:-10}
      - DATABASE_POOL_MAX_OVERFLOW=${DATABASE_POOL_MAX_OVERFLOW:-20}
      - DATABASE_POOL_TIMEOUT=${DATABASE_POOL_TIMEOUT:-30}
      - DATABASE_POOL_RECYCLE=${DATABASE_POOL_RECYCLE:-3600}
    env_file:
      - .env.postgresql
