# PostgreSQL Database Configuration
# Use this when you want PostgreSQL instead of SQLite

# =================================
# PostgreSQL Connection Settings
# =================================
POSTGRES_USER=openwebui
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_DB=openwebui
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Database URL for Open WebUI (overrides SQLite)
# Note: Make sure this matches your actual password
DATABASE_URL=***********************************************************************/openwebui

# =================================
# PostgreSQL Performance Settings
# =================================
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
POSTGRES_MAINTENANCE_WORK_MEM=64MB
POSTGRES_CHECKPOINT_COMPLETION_TARGET=0.9
POSTGRES_WAL_BUFFERS=16MB
POSTGRES_DEFAULT_STATISTICS_TARGET=100
POSTGRES_RANDOM_PAGE_COST=1.1
POSTGRES_EFFECTIVE_IO_CONCURRENCY=200

# =================================
# Connection Pool Settings
# =================================
POSTGRES_MAX_CONNECTIONS=100
POSTGRES_SHARED_PRELOAD_LIBRARIES=pg_stat_statements

# =================================
# Backup & Recovery
# =================================
POSTGRES_BACKUP_ENABLED=true
POSTGRES_BACKUP_SCHEDULE=0 2 * * *
POSTGRES_BACKUP_RETENTION_DAYS=7

# =================================
# Development Settings
# =================================
POSTGRES_LOG_STATEMENT=none
POSTGRES_LOG_MIN_DURATION_STATEMENT=-1
