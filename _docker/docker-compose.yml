networks:
  openwebui-network:
    driver: bridge

volumes:
  open-webui-data:
    driver: local
  litellm-postgres-data:
    driver: local

services:
  # Open WebUI - Main application
  openwebui:
    image: ghcr.io/open-webui/open-webui:main
    # image: ghcr.io/open-webui/open-webui:cuda # Use this if you wish to use the cuda version to run reranking models and transcription models on an nvidia gpu.
    container_name: openwebui
    restart: unless-stopped
    ports:
      - "${OPENWEBUI_PORT:-3000}:8080"
    depends_on:
      - litellm
    networks:
      - openwebui-network
    volumes:
      - open-webui-data:/app/backend/data

    environment:
      # Basic configuration
      - ENV=dev
      - WEBUI_NAME=${WEBUI_NAME:-Open WebUI}
      - WEBUI_URL=${WEBUI_URL:-http://localhost:3000}
      - CUSTOM_NAME=${CUSTOM_NAME:-Open WebUI}
      - USE_CUDA_DOCKER=${USE_CUDA_DOCKER:-False}

      # Authentication
      - ENABLE_SIGNUP=${ENABLE_SIGNUP:-false}
      - DEFAULT_USER_ROLE=${DEFAULT_USER_ROLE:-pending}
      - WEBUI_SECRET_KEY=${WEBUI_SECRET_KEY}

      # Disable unnecessary features for production
      - ENABLE_ADMIN_EXPORT=${ENABLE_ADMIN_EXPORT:-False}
      - ENABLE_ADMIN_CHAT_ACCESS=${ENABLE_ADMIN_CHAT_ACCESS:-False}

      # User Permissions
      - USER_PERMISSIONS_CHAT_CALL=${USER_PERMISSIONS_CHAT_CALL:-False}
      - USER_PERMISSIONS_WORKSPACE_MODELS_ACCESS=${USER_PERMISSIONS_WORKSPACE_MODELS_ACCESS:-True}
      - USER_PERMISSIONS_WORKSPACE_KNOWLEDGE_ACCESS=${USER_PERMISSIONS_WORKSPACE_KNOWLEDGE_ACCESS:-True}
      - USER_PERMISSIONS_WORKSPACE_PROMPTS_ACCESS=${USER_PERMISSIONS_WORKSPACE_PROMPTS_ACCESS:-True}

      # RAG (Retrieval-Augmented Generation) Configuration
      - RAG_EMBEDDING_ENGINE=${RAG_EMBEDDING_ENGINE:-openai}
      - RAG_EMBEDDING_MODEL=${RAG_EMBEDDING_MODEL:-huggingface/Qwen/Qwen3-Embedding-0.6B}
      - ENABLE_RAG_HYBRID_SEARCH=${ENABLE_RAG_HYBRID_SEARCH:-true}
      - RAG_TOP_K=${RAG_TOP_K:-5}
      - RAG_TOP_K_RERANKER=${RAG_TOP_K_RERANKER:-5}
      - RAG_RELEVANCE_THRESHOLD=${RAG_RELEVANCE_THRESHOLD:-20.0}
      - RAG_TEXT_SPLITTER=${RAG_TEXT_SPLITTER:-character}
      - CHUNK_SIZE=${CHUNK_SIZE:-1500}
      - CHUNK_OVERLAP=${CHUNK_OVERLAP:-300}
      - RAG_OPENAI_API_BASE_URL=${RAG_OPENAI_API_BASE_URL:-http://litellm:4000/}
      - RAG_OPENAI_API_KEY=${RAG_OPENAI_API_KEY}
      - RAG_EMBEDDING_OPENAI_BATCH_SIZE=${RAG_EMBEDDING_OPENAI_BATCH_SIZE:-10}
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  ##############################
  # LiteLLM - AI Gateway/Proxy
  ##############################

  # PostgreSQL Database for LiteLLM
  litellm-db:
    image: postgres:17
    container_name: litellm-db
    restart: always
    environment:
      - POSTGRES_DB=litellm
      - POSTGRES_USER=litellm
      - POSTGRES_PASSWORD=litellm_secure_2024
    volumes:
      - litellm-postgres-data:/var/lib/postgresql/data
    networks:
      - openwebui-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -d litellm -U litellm"]
      interval: 1s
      timeout: 5s
      retries: 10
  # LiteLLM - AI Gateway/Proxy
  litellm:
    image: ghcr.io/berriai/litellm:main-stable
    container_name: litellm
    restart: unless-stopped
    ports:
      - "${LITELLM_PORT:-4000}:4000"
    networks:
      - openwebui-network
    volumes:
      - ./config/litellm-config.yaml:/app/config.yaml
    command:
      - "--config"
      - "/app/config.yaml"

    environment:
      - LITELLM_MASTER_KEY=${LITELLM_MASTER_KEY}
      - LITELLM_SALT_KEY=${LITELLM_SALT_KEY}
      - DATABASE_URL=${LITELLM_DATABASE_URL}
      - STORE_MODEL_IN_DB=True
    depends_on:
      litellm-db:
        condition: service_healthy
    env_file:
      - .env
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--quiet",
          "--tries=1",
          "--spider",
          "http://localhost:4000/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
