# LiteLLM Configuration Example
# For more detailed information on centralized credential management, visit:
# https://docs.litellm.ai/docs/proxy/configs#centralized-credential-management

# Example credential configuration (uncomment and modify as needed)
# credential_list:
#   - credential_name: example_credentials
#     credential_values:
#       api_key: "os.environ/YOUR_API_KEY"
#       api_base: "os.environ/YOUR_API_BASE"
#     credential_info:
#       description: "Example credentials for your provider"

# Example model configuration (uncomment and modify as needed)
# model_list:
#   - model_name: your-model-name
#     litellm_params:
#       model: provider/model-name
#       litellm_credential_name: example_credentials

litellm_settings:
  drop_params: True

general_settings:
  database_url: "os.environ/LITELLM_DATABASE_URL"
