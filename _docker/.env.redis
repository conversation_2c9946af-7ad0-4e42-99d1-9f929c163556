# Redis Configuration
# Use this for session management and caching

# =================================
# Redis Connection Settings
# =================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_DB=0

# Redis URL for Open WebUI
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# =================================
# Session Configuration
# =================================
WEBUI_SESSION_COOKIE_SAME_SITE=lax
WEBUI_SESSION_COOKIE_SECURE=false
WEBUI_SESSION_COOKIE_HTTPONLY=true
SESSION_STORE=redis

# =================================
# Cache Configuration
# =================================
ENABLE_REDIS_CACHE=true
REDIS_CACHE_TTL=3600
REDIS_CACHE_MAX_SIZE=1000

# =================================
# Redis Performance Settings
# =================================
REDIS_MAXMEMORY=512mb
REDIS_MAXMEMORY_POLICY=allkeys-lru
REDIS_SAVE_ENABLED=true
REDIS_SAVE_SCHEDULE=900 1 300 10 60 10000

# =================================
# Redis Security
# =================================
REDIS_PROTECTED_MODE=yes
REDIS_BIND_ADDRESS=127.0.0.1
REDIS_REQUIREPASS=${REDIS_PASSWORD}

# =================================
# Development Settings
# =================================
REDIS_LOG_LEVEL=notice
REDIS_DATABASES=16
