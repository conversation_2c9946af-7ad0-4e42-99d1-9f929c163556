# MinIO S3-Compatible Storage
# Usage: docker-compose -f docker-compose.yml -f docker-compose.s3.yml up -d

volumes:
  minio-data:
    driver: local

services:
  # MinIO S3-Compatible Storage
  minio:
    image: minio/minio:latest
    container_name: openwebui-minio
    restart: unless-stopped
    networks:
      - openwebui-network
    volumes:
      - minio-data:/data
    ports:
      - "${MINIO_API_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}
      - MINIO_BROWSER_REDIRECT_URL=${MINIO_BROWSER_REDIRECT_URL:-http://localhost:9001}
      - MINIO_SERVER_URL=${MINIO_SERVER_URL:-http://localhost:9000}
    env_file:
      - .env
      - .env.s3
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s

  # MinIO Client for bucket creation
  minio-client:
    image: minio/mc:latest
    container_name: openwebui-minio-client
    networks:
      - openwebui-network
    depends_on:
      minio:
        condition: service_healthy
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME:-openwebui}
    env_file:
      - .env
      - .env.s3
    entrypoint: >
      /bin/sh -c "
      until (/usr/bin/mc alias set minio http://minio:9000 $${MINIO_ROOT_USER} $${MINIO_ROOT_PASSWORD}) do echo '...waiting...' && sleep 1; done;
      /usr/bin/mc mb minio/$${MINIO_BUCKET_NAME} --ignore-existing;
      echo 'Bucket created successfully. Note: Bucket is private by default for security.';
      exit 0;
      "

  # Override Open WebUI to use MinIO S3
  openwebui:
    depends_on:
      minio:
        condition: service_healthy
      litellm:
        condition: service_healthy
    environment:
      # S3 Storage Configuration
      - STORAGE_PROVIDER=s3
      - S3_ACCESS_KEY_ID=${MINIO_ROOT_USER:-minioadmin}
      - S3_SECRET_ACCESS_KEY=${MINIO_ROOT_PASSWORD}
      - S3_BUCKET_NAME=${MINIO_BUCKET_NAME:-openwebui}
      - S3_ENDPOINT_URL=http://minio:9000
      - S3_REGION_NAME=${S3_REGION_NAME:-us-east-1}
      - S3_USE_ACCELERATE_ENDPOINT=false
      - S3_ENABLE_TAGGING=false
