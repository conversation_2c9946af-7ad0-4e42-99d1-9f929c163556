# Vector Database Configuration (Qdrant)
# Use this for RAG (Retrieval-Augmented Generation) functionality

# =================================
# Qdrant Connection Settings
# =================================
QDRANT_HOST=qdrant
QDRANT_PORT=6333
QDRANT_API_KEY=your_secure_qdrant_api_key_here

# Qdrant URL for Open WebUI
QDRANT_URL=http://${QDRANT_HOST}:${QDRANT_PORT}

# =================================
# Vector Store Configuration
# =================================
VECTOR_DB=qdrant
