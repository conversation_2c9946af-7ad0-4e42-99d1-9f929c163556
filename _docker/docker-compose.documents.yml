# Document Processing Services
# Usage: docker-compose -f docker-compose.yml -f docker-compose.documents.yml up -d

services:
  # Apache Tika Document Processing Service
  tika:
    image: apache/tika:latest-full
    container_name: tika
    ports:
      - "9998:9998"
    restart: unless-stopped
    networks:
      - openwebui-network

  # Override Open WebUI to use document processing services
  openwebui:
    depends_on:
      tika:
        condition: service_started
      litellm:
        condition: service_healthy
    environment:
      # Document Processing Configuration
      - CONTENT_EXTRACTION_ENGINE=tika
      - TIKA_SERVER_URL=http://tika:9998

      # File processing settings
      - RAG_ALLOWED_FILE_EXTENSIONS=${RAG_ALLOWED_FILE_EXTENSIONS:-["pdf","docx","txt","md","csv","xlsx","pptx"]}
      - CHUNK_SIZE=${CHUNK_SIZE:-1000}
      - CHUNK_OVERLAP=${CHUNK_OVERLAP:-200}
      - RAG_EMBEDDING_BATCH_SIZE=10
